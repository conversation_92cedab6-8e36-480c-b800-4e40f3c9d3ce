using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data;
using IPIS.Services;
using IPIS.Repositories;
using IPIS.Utils;
using System.Collections.Generic;
using System.Linq;

namespace IPIS.Forms.Train
{
    public partial class AddTrainDetailsForm : Form
    {
        private TrainService trainService;
        private TrainTypeService trainTypeService;
        private StationService stationService;
        private readonly ToastNotification toast;
        private Dictionary<string, string> stationNamesAndCodes; // Dictionary to map station names to codes

        // Control declarations
        private ComboBox cmbTrainNo;
        private TextBox txtNewTrainNo;
        private TextBox txtTrainNameEng;
        private ComboBox cmbTrainType;
        private ComboBox cmbSourceStation;
        private ComboBox cmbDestinationStation;
        private NumericUpDown numArrHour;
        private NumericUpDown numArrMinute;
        private ComboBox cmbTrainAD;
        private NumericUpDown numDepHour;
        private NumericUpDown numDepMinute;
        private ComboBox cmbPFNo;
        private CheckBox chkAllDays;
        private CheckBox chkSun;
        private CheckBox chkMon;
        private CheckBox chkTue;
        private CheckBox chkWed;
        private CheckBox chkThu;
        private CheckBox chkFri;
        private CheckBox chkSat;
        private ComboBox cmbVia1;
        private ComboBox cmbVia2;
        private ComboBox cmbVia3;
        private ComboBox cmbVia4;
        private FlowLayoutPanel arrTimePanel;
        private FlowLayoutPanel depTimePanel;
        private Label statusLabel;
        private System.Windows.Forms.Timer statusTimer;

        public AddTrainDetailsForm()
        {
            InitializeComponent();
            trainService = new TrainService(new SQLiteTrainRepository());
            trainTypeService = new TrainTypeService(new SQLiteTrainTypeRepository());
            stationService = new StationService(new SQLiteStationRepository());
            toast = new ToastNotification(this);
            stationNamesAndCodes = new Dictionary<string, string>();
            PopulateComboBoxes();
            LoadTrainNumbers();
        }

        private void InitializeComponent()
        {
            this.Text = "Add Train Details";
            this.Size = new Size(900, 750);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.BackColor = UIStyler.Colors.Background;

            // Main Table Layout Panel
            TableLayoutPanel mainLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 1,
                RowCount = 6,
                Padding = new Padding(16),
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
            };
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 60F)); // Header
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 40F)); // Train Details
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 20F)); // Running Days
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 20F)); // Via Station
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 80F)); // Buttons
            mainLayout.RowStyles.Add(new RowStyle(SizeType.Absolute, 40F)); // Status

            // Header Section
            CreateHeaderSection(mainLayout);

            // Train Details Section
            CreateTrainDetailsSection(mainLayout);

            // Running Days Section
            CreateRunningDaysSection(mainLayout);

            // Via Station Section
            CreateViaStationSection(mainLayout);

            // Buttons Section
            CreateButtonsSection(mainLayout);

            // Status Section
            CreateStatusSection(mainLayout);

            this.Controls.Add(mainLayout);
        }

        private void CreateHeaderSection(TableLayoutPanel mainLayout)
        {
            Panel headerPanel = new Panel
            {
                Dock = DockStyle.Fill,
                AutoSize = true
            };
            UIStyler.ApplyPanelStyle(headerPanel, "card");

            Label headingLabel = new Label
            {
                Text = "Add Train Details",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft
            };
            UIStyler.ApplyLabelStyle(headingLabel, "h5");

            headerPanel.Controls.Add(headingLabel);
            mainLayout.Controls.Add(headerPanel, 0, 0);
        }

        private void CreateTrainDetailsSection(TableLayoutPanel mainLayout)
        {
            GroupBox grpTrainDetails = new GroupBox { Text = "Train Details", Dock = DockStyle.Fill };
            UIStyler.ApplyGroupBoxStyle(grpTrainDetails);

            TableLayoutPanel trainDetailsLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 5, // Increased to 5 rows to add Train No at the top
                CellBorderStyle = TableLayoutPanelCellBorderStyle.None,
                Padding = new Padding(12)
            };
            trainDetailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140F));
            trainDetailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            trainDetailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 140F));
            trainDetailsLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));

            // Row 0: Train No and New Train No
            cmbTrainNo = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList, MinimumSize = new Size(120, 0) };
            cmbTrainNo.SelectedIndexChanged += cmbTrainNo_SelectedIndexChanged;
            txtNewTrainNo = new TextBox { PlaceholderText = "Enter new train number", MinimumSize = new Size(120, 0) };

            CreateLabelAndControl(trainDetailsLayout, "Train Number:", cmbTrainNo, 0, 0);
            CreateLabelAndControl(trainDetailsLayout, "New Train No:", txtNewTrainNo, 2, 0);

            // Row 1: Train Name and Type
            CreateLabelAndControl(trainDetailsLayout, "Train Name:", txtTrainNameEng = new TextBox(), 0, 1);
            CreateLabelAndControl(trainDetailsLayout, "Train Type:", cmbTrainType = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList }, 2, 1);

            // Row 2: Source and Destination
            CreateLabelAndControl(trainDetailsLayout, "Source Station:", cmbSourceStation = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList }, 0, 2);
            CreateLabelAndControl(trainDetailsLayout, "Destination Station:", cmbDestinationStation = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList }, 2, 2);

            // Row 3: Arrival Time and Train AD
            CreateLabelAndControl(trainDetailsLayout, "Arrival Time:", CreateTimePanel(out numArrHour, out numArrMinute), 0, 3);
            CreateLabelAndControl(trainDetailsLayout, "Train Arr/Dep:", cmbTrainAD = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList }, 2, 3);

            // Row 4: Departure Time and Platform
            CreateLabelAndControl(trainDetailsLayout, "Departure Time:", CreateTimePanel(out numDepHour, out numDepMinute), 0, 4);
            CreateLabelAndControl(trainDetailsLayout, "Platform No:", cmbPFNo = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList }, 2, 4);

            grpTrainDetails.Controls.Add(trainDetailsLayout);
            mainLayout.Controls.Add(grpTrainDetails, 0, 1);
        }

        private void CreateRunningDaysSection(TableLayoutPanel mainLayout)
        {
            GroupBox grpRunningDays = new GroupBox { Text = "Running Days", Dock = DockStyle.Fill };
            UIStyler.ApplyGroupBoxStyle(grpRunningDays);

            TableLayoutPanel runningDaysLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 2,
                Padding = new Padding(12)
            };

            // Create checkboxes
            chkAllDays = CreateCheckBox("All Days");
            chkSun = CreateCheckBox("Sunday");
            chkMon = CreateCheckBox("Monday");
            chkTue = CreateCheckBox("Tuesday");
            chkWed = CreateCheckBox("Wednesday");
            chkThu = CreateCheckBox("Thursday");
            chkFri = CreateCheckBox("Friday");
            chkSat = CreateCheckBox("Saturday");

            // Restore event handler for All Days
            chkAllDays.CheckedChanged += chkAllDays_CheckedChanged;

            // Add to layout
            runningDaysLayout.Controls.Add(chkAllDays, 0, 0);
            runningDaysLayout.Controls.Add(chkSun, 1, 0);
            runningDaysLayout.Controls.Add(chkMon, 2, 0);
            runningDaysLayout.Controls.Add(chkTue, 3, 0);
            runningDaysLayout.Controls.Add(chkWed, 0, 1);
            runningDaysLayout.Controls.Add(chkThu, 1, 1);
            runningDaysLayout.Controls.Add(chkFri, 2, 1);
            runningDaysLayout.Controls.Add(chkSat, 3, 1);

            grpRunningDays.Controls.Add(runningDaysLayout);
            mainLayout.Controls.Add(grpRunningDays, 0, 2);
        }

        private void CreateViaStationSection(TableLayoutPanel mainLayout)
        {
            GroupBox grpViaStation = new GroupBox { Text = "Via Stations", Dock = DockStyle.Fill };
            UIStyler.ApplyGroupBoxStyle(grpViaStation);

            TableLayoutPanel viaStationLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                ColumnCount = 4,
                RowCount = 2,
                Padding = new Padding(12)
            };
            viaStationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            viaStationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));
            viaStationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 40F));
            viaStationLayout.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 50F));

            // Create via station controls
            cmbVia1 = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            cmbVia2 = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            cmbVia3 = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };
            cmbVia4 = new ComboBox { DropDownStyle = ComboBoxStyle.DropDownList };

            UIStyler.ApplyComboBoxStyle(cmbVia1, "medium");
            UIStyler.ApplyComboBoxStyle(cmbVia2, "medium");
            UIStyler.ApplyComboBoxStyle(cmbVia3, "medium");
            UIStyler.ApplyComboBoxStyle(cmbVia4, "medium");

            // Add to layout
            viaStationLayout.Controls.Add(CreateLabel("1"), 0, 0);
            viaStationLayout.Controls.Add(cmbVia1, 1, 0);
            viaStationLayout.Controls.Add(CreateLabel("3"), 2, 0);
            viaStationLayout.Controls.Add(cmbVia3, 3, 0);
            viaStationLayout.Controls.Add(CreateLabel("2"), 0, 1);
            viaStationLayout.Controls.Add(cmbVia2, 1, 1);
            viaStationLayout.Controls.Add(CreateLabel("4"), 2, 1);
            viaStationLayout.Controls.Add(cmbVia4, 3, 1);

            grpViaStation.Controls.Add(viaStationLayout);
            mainLayout.Controls.Add(grpViaStation, 0, 3);
        }

        private void CreateButtonsSection(TableLayoutPanel mainLayout)
        {
            FlowLayoutPanel buttonPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                FlowDirection = FlowDirection.LeftToRight,
                Padding = new Padding(8),
                Margin = new Padding(0)
            };

            Button btnSave = new Button { Text = "Save" };
            Button btnAdd = new Button { Text = "Add Another" };
            Button btnEdit = new Button { Text = "Edit" };
            Button btnDelete = new Button { Text = "Delete" };
            Button btnExit = new Button { Text = "Exit" };

            // Apply button styles
            ButtonStyler.ApplyStandardStyle(btnSave, "success", "medium");
            ButtonStyler.ApplyStandardStyle(btnAdd, "info", "medium");
            ButtonStyler.ApplyStandardStyle(btnEdit, "warning", "medium");
            ButtonStyler.ApplyStandardStyle(btnDelete, "danger", "medium");
            ButtonStyler.ApplyStandardStyle(btnExit, "secondary", "medium");

            // Wire up events
            btnSave.Click += BtnSave_Click;
            btnAdd.Click += btnAdd_Click;
            btnEdit.Click += btnEdit_Click;
            btnDelete.Click += btnDelete_Click;
            btnExit.Click += (s, e) => this.Close();

            buttonPanel.Controls.Add(btnSave);
            buttonPanel.Controls.Add(btnAdd);
            buttonPanel.Controls.Add(btnEdit);
            buttonPanel.Controls.Add(btnDelete);
            buttonPanel.Controls.Add(btnExit);

            mainLayout.Controls.Add(buttonPanel, 0, 4);
        }

        private void CreateStatusSection(TableLayoutPanel mainLayout)
        {
            statusLabel = new Label { Text = "Ready", TextAlign = ContentAlignment.MiddleLeft };
            UIStyler.ApplyLabelStyle(statusLabel, "small");
            mainLayout.Controls.Add(statusLabel, 0, 5);
        }

        private void CreateLabelAndControl(TableLayoutPanel layout, string labelText, Control control, int column, int row)
        {
            Label label = CreateLabel(labelText);
            UIStyler.ApplyLabelStyle(label, "body");

            if (control is TextBox textBox)
                UIStyler.ApplyTextBoxStyle(textBox, "medium");
            else if (control is ComboBox comboBox)
                UIStyler.ApplyComboBoxStyle(comboBox, "medium");

            layout.Controls.Add(label, column, row);
            layout.Controls.Add(control, column + 1, row);
        }

        private Label CreateLabel(string text)
        {
            Label label = new Label { Text = text };
            UIStyler.ApplyLabelStyle(label, "body");
            return label;
        }

        private CheckBox CreateCheckBox(string text)
        {
            CheckBox checkBox = new CheckBox { Text = text };
            UIStyler.ApplyCheckBoxStyle(checkBox);
            return checkBox;
        }

        private Panel CreateTimePanel(out NumericUpDown hour, out NumericUpDown minute)
        {
            Panel timePanel = new Panel { AutoSize = true };
            FlowLayoutPanel flowPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.LeftToRight,
                AutoSize = true,
                Margin = new Padding(0)
            };

            hour = new NumericUpDown { Minimum = 0, Maximum = 23, Width = 50 };
            minute = new NumericUpDown { Minimum = 0, Maximum = 59, Width = 50 };

            UIStyler.ApplyNumericUpDownStyle(hour, "small");
            UIStyler.ApplyNumericUpDownStyle(minute, "small");

            Label colonLabel = CreateLabel(":");
            colonLabel.Margin = new Padding(4, 6, 4, 0);

            flowPanel.Controls.Add(hour);
            flowPanel.Controls.Add(colonLabel);
            flowPanel.Controls.Add(minute);

            timePanel.Controls.Add(flowPanel);
            return timePanel;
        }

        private void PopulateComboBoxes()
        {
            // Load train types from database
            LoadTrainTypes();

            // Load stations from database
            LoadStations();

            cmbTrainAD.Items.AddRange(new string[] { "A", "D" });
            cmbPFNo.Items.AddRange(new string[] { "1", "2", "3", "4", "5", "6" });
        }

        private void LoadTrainTypes()
        {
            try
            {
                cmbTrainType.Items.Clear();
                var trainTypeNames = trainTypeService.GetActiveTrainTypeNames();
                cmbTrainType.Items.AddRange(trainTypeNames.ToArray());
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading train types: {ex.Message}");
                // Fallback to hardcoded values if database fails
                cmbTrainType.Items.AddRange(new string[] { "DEMU", "Express", "Passenger", "Garib Rath", "Superfast" });
            }
        }

        private void LoadStations()
        {
            try
            {
                // Get station names and codes mapping
                stationNamesAndCodes = stationService.GetStationNamesAndCodes();

                // Create formatted station display strings
                string[] stationDisplayArray = stationNamesAndCodes
                    .Select(kvp => $"{kvp.Key} - {kvp.Value}")
                    .ToArray();

                // Clear existing items
                cmbSourceStation.Items.Clear();
                cmbDestinationStation.Items.Clear();
                cmbVia1.Items.Clear();
                cmbVia2.Items.Clear();
                cmbVia3.Items.Clear();
                cmbVia4.Items.Clear();

                // Add formatted station strings to all station ComboBoxes
                cmbSourceStation.Items.AddRange(stationDisplayArray);
                cmbDestinationStation.Items.AddRange(stationDisplayArray);
                cmbVia1.Items.AddRange(stationDisplayArray);
                cmbVia2.Items.AddRange(stationDisplayArray);
                cmbVia3.Items.AddRange(stationDisplayArray);
                cmbVia4.Items.AddRange(stationDisplayArray);
            }
            catch (Exception ex)
            {
                toast.ShowError($"Error loading stations: {ex.Message}");
                // Fallback to hardcoded values if database fails
                string[] fallbackStations = { "BNW - BNW", "ROK - ROK", "RE - RE", "HNS - HNS", "NDLS - NDLS", "SSB - SSB", "CGD - CGD", "NNO - NNO" };
                stationNamesAndCodes = new Dictionary<string, string>();
                // Parse fallback stations to maintain the original mapping structure
                foreach (string stationDisplay in fallbackStations)
                {
                    string[] parts = stationDisplay.Split(new string[] { " - " }, StringSplitOptions.None);
                    if (parts.Length == 2)
                    {
                        stationNamesAndCodes[parts[0]] = parts[1];
                    }
                }
                cmbSourceStation.Items.AddRange(fallbackStations);
                cmbDestinationStation.Items.AddRange(fallbackStations);
                cmbVia1.Items.AddRange(fallbackStations);
                cmbVia2.Items.AddRange(fallbackStations);
                cmbVia3.Items.AddRange(fallbackStations);
                cmbVia4.Items.AddRange(fallbackStations);
            }
        }

        /// <summary>
        /// Public method to refresh station data in ComboBoxes.
        /// This can be called when new stations are added from AddStationDetailsForm.
        /// </summary>
        public void RefreshStationData()
        {
            LoadStations();
        }

        private void LoadTrainNumbers()
        {
            DataTable trains = trainService.GetAllTrains();
            cmbTrainNo.Items.Clear();
            foreach (DataRow row in trains.Rows)
            {
                string trainNo = row["Train_No"].ToString();
                string trainName = row["Train_NameEng"].ToString();
                string displayText = $"{trainNo} - {trainName}";
                cmbTrainNo.Items.Add(displayText);
            }
        }

        private void cmbTrainNo_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbTrainNo.SelectedItem != null)
            {
                string selectedTrainDisplay = cmbTrainNo.SelectedItem.ToString();
                // Extract train number from "train number - train name" format
                string selectedTrainNo = selectedTrainDisplay.Split(new string[] { " - " }, StringSplitOptions.None)[0];
                LoadTrainData(selectedTrainNo);
            }
        }

        private void LoadTrainData(string trainNo)
        {
            DataTable trains = trainService.GetAllTrains();
            DataRow[] selectedRows = trains.Select($"Train_No = '{trainNo}'");

            if (selectedRows.Length > 0)
            {
                DataRow row = selectedRows[0];
                txtTrainNameEng.Text = row["Train_NameEng"].ToString();
                cmbTrainType.SelectedItem = row["Train_Type"].ToString();
                cmbTrainAD.SelectedItem = row["Train_AD"].ToString();

                string schAT = row["Sch_AT"].ToString();
                if (TimeSpan.TryParse(schAT, out TimeSpan arrTime))
                {
                    numArrHour.Value = arrTime.Hours;
                    numArrMinute.Value = arrTime.Minutes;
                }

                string schDT = row["Sch_DT"].ToString();
                if (TimeSpan.TryParse(schDT, out TimeSpan depTime))
                {
                    numDepHour.Value = depTime.Hours;
                    numDepMinute.Value = depTime.Minutes;
                }

                cmbPFNo.SelectedItem = row["Sch_PF"].ToString();
                cmbSourceStation.SelectedItem = GetStationDisplayText(row["Src_Stn"].ToString());
                cmbDestinationStation.SelectedItem = GetStationDisplayText(row["Desti_Stn"].ToString());

                cmbVia1.SelectedItem = row["Via1"] != DBNull.Value ? GetStationDisplayText(row["Via1"].ToString()) : "";
                cmbVia2.SelectedItem = row["Via2"] != DBNull.Value ? GetStationDisplayText(row["Via2"].ToString()) : "";
                cmbVia3.SelectedItem = row["Via3"] != DBNull.Value ? GetStationDisplayText(row["Via3"].ToString()) : "";
                cmbVia4.SelectedItem = row["Via4"] != DBNull.Value ? GetStationDisplayText(row["Via4"].ToString()) : "";

                chkAllDays.Checked = Convert.ToBoolean(row["All_Days"]);
                chkSun.Checked = Convert.ToBoolean(row["Chk_Sun"]);
                chkMon.Checked = Convert.ToBoolean(row["Chk_Mon"]);
                chkTue.Checked = Convert.ToBoolean(row["Chk_Tue"]);
                chkWed.Checked = Convert.ToBoolean(row["Chk_Wed"]);
                chkThu.Checked = Convert.ToBoolean(row["Chk_Thu"]);
                chkFri.Checked = Convert.ToBoolean(row["Chk_Fri"]);
                chkSat.Checked = Convert.ToBoolean(row["Chk_Sat"]);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            Console.WriteLine("BtnSave_Click triggered.");

            string trainNo = cmbTrainNo.Text;
            if (string.IsNullOrWhiteSpace(trainNo) && !string.IsNullOrWhiteSpace(txtNewTrainNo.Text))
            {
                trainNo = txtNewTrainNo.Text.Trim();
            }

            Console.WriteLine($"TrainNo: {trainNo}");

            if (string.IsNullOrWhiteSpace(trainNo))
            {
                toast.ShowError("Train Number cannot be empty.");
                return;
            }

            string trainName = txtTrainNameEng.Text.Trim();
            string trainType = cmbTrainType.SelectedItem?.ToString();
            string trainAD = cmbTrainAD.SelectedItem?.ToString();
            string schAT = $"{numArrHour.Value:00}:{numArrMinute.Value:00}";
            string schDT = $"{numDepHour.Value:00}:{numDepMinute.Value:00}";
            string schPF = cmbPFNo.SelectedItem?.ToString();
            string srcStn = GetStationCodeFromDisplayText(cmbSourceStation.SelectedItem?.ToString());
            string destiStn = GetStationCodeFromDisplayText(cmbDestinationStation.SelectedItem?.ToString());

            Console.WriteLine($"TrainName: {trainName}, TrainType: {trainType}, TrainAD: {trainAD}, SchAT: {schAT}, SchDT: {schDT}, SchPF: {schPF}, SrcStn: {srcStn}, DestiStn: {destiStn}");

            string[] viaStations = new string[4];
            viaStations[0] = GetStationCodeFromDisplayText(cmbVia1.SelectedItem?.ToString());
            viaStations[1] = GetStationCodeFromDisplayText(cmbVia2.SelectedItem?.ToString());
            viaStations[2] = GetStationCodeFromDisplayText(cmbVia3.SelectedItem?.ToString());
            viaStations[3] = GetStationCodeFromDisplayText(cmbVia4.SelectedItem?.ToString());

            Console.WriteLine($"Via Stations: {string.Join(", ", viaStations)}");

            bool[] operatingDays = new bool[8]; // All Days, Sun, Mon, Tue, Wed, Thu, Fri, Sat
            operatingDays[0] = chkAllDays.Checked;
            operatingDays[1] = chkSun.Checked;
            operatingDays[2] = chkMon.Checked;
            operatingDays[3] = chkTue.Checked;
            operatingDays[4] = chkWed.Checked;
            operatingDays[5] = chkThu.Checked;
            operatingDays[6] = chkFri.Checked;
            operatingDays[7] = chkSat.Checked;

            Console.WriteLine($"Operating Days: {string.Join(", ", operatingDays)}");

            try
            {
                DataTable existingTrains = trainService.GetAllTrains();
                DataRow[] foundRows = existingTrains.Select($"Train_No = '{trainNo}'");

                if (foundRows.Length > 0)
                {
                    Console.WriteLine("Updating existing train...");
                    trainService.UpdateTrain(trainNo, trainName, trainType, trainAD, schAT, schDT, schPF, srcStn, destiStn, viaStations, operatingDays);
                    toast.ShowSuccess("Train details updated successfully!");
                }
                else
                {
                    Console.WriteLine("Adding new train...");
                    trainService.AddTrain(trainNo, trainName, trainType, trainAD, schAT, schDT, schPF, srcStn, destiStn, viaStations, operatingDays);
                    toast.ShowSuccess("Train details added successfully!");
                }
                ClearForm();
                LoadTrainNumbers();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
                toast.ShowError($"Error saving train details: {ex.Message}");
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            ClearForm();
            txtNewTrainNo.Focus();
            toast.ShowInfo("Ready to add new train");
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (cmbTrainNo.SelectedItem == null)
            {
                toast.ShowError("Please select a train to edit.");
                return;
            }
            toast.ShowInfo("Edit mode enabled");
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (cmbTrainNo.SelectedItem == null)
            {
                toast.ShowError("Please select a train to delete.");
                return;
            }

            string trainDisplayToDelete = cmbTrainNo.SelectedItem.ToString();
            // Extract train number from "train number - train name" format
            string trainNoToDelete = trainDisplayToDelete.Split(new string[] { " - " }, StringSplitOptions.None)[0];
            DialogResult confirmResult = MessageBox.Show($"Are you sure you want to delete train {trainDisplayToDelete}?", "Confirm Delete", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (confirmResult == DialogResult.Yes)
            {
                try
                {
                    trainService.DeleteTrain(trainNoToDelete);
                    toast.ShowSuccess("Train deleted successfully!");
                    ClearForm();
                    LoadTrainNumbers();
                }
                catch (Exception ex)
                {
                    toast.ShowError($"Error deleting train: {ex.Message}");
                }
            }
        }

        private void chkAllDays_CheckedChanged(object sender, EventArgs e)
        {
            bool isChecked = chkAllDays.Checked;
            chkSun.Checked = isChecked;
            chkMon.Checked = isChecked;
            chkTue.Checked = isChecked;
            chkWed.Checked = isChecked;
            chkThu.Checked = isChecked;
            chkFri.Checked = isChecked;
            chkSat.Checked = isChecked;
        }

        private void ClearForm()
        {
            cmbTrainNo.Text = string.Empty;
            txtNewTrainNo.Text = string.Empty;
            txtTrainNameEng.Text = string.Empty;
            cmbTrainType.SelectedIndex = -1;
            cmbTrainAD.SelectedIndex = -1;
            numArrHour.Value = 0;
            numArrMinute.Value = 0;
            numDepHour.Value = 0;
            numDepMinute.Value = 0;
            cmbPFNo.SelectedIndex = -1;
            cmbSourceStation.SelectedIndex = -1;
            cmbDestinationStation.SelectedIndex = -1;
            cmbVia1.SelectedIndex = -1;
            cmbVia2.SelectedIndex = -1;
            cmbVia3.SelectedIndex = -1;
            cmbVia4.SelectedIndex = -1;
            chkAllDays.Checked = false;
            chkSun.Checked = false;
            chkMon.Checked = false;
            chkTue.Checked = false;
            chkWed.Checked = false;
            chkThu.Checked = false;
            chkFri.Checked = false;
            chkSat.Checked = false;
        }

        private string GetStationCode(string stationName)
        {
            if (string.IsNullOrEmpty(stationName))
                return null;

            if (stationNamesAndCodes != null && stationNamesAndCodes.TryGetValue(stationName, out string code))
            {
                return code;
            }
            return stationName; // Fallback to station name if code not found
        }

        private string GetStationName(string stationCode)
        {
            if (string.IsNullOrEmpty(stationCode))
                return null;

            if (stationNamesAndCodes != null)
            {
                foreach (var kvp in stationNamesAndCodes)
                {
                    if (kvp.Value == stationCode)
                    {
                        return kvp.Key;
                    }
                }
            }
            return stationCode; // Fallback to station code if name not found
        }

        private string GetStationDisplayText(string stationCode)
        {
            if (string.IsNullOrEmpty(stationCode))
                return "";

            string stationName = GetStationName(stationCode);
            if (!string.IsNullOrEmpty(stationName))
            {
                return $"{stationName} - {stationCode}";
            }
            return stationCode; // Fallback to station code if name not found
        }

        private string GetStationCodeFromDisplayText(string displayText)
        {
            if (string.IsNullOrEmpty(displayText))
                return null;

            // Extract station code from "station name - station code" format
            string[] parts = displayText.Split(new string[] { " - " }, StringSplitOptions.None);
            if (parts.Length == 2)
            {
                return parts[1]; // Return the station code part
            }
            return displayText; // Fallback to original text if format doesn't match
        }
    }
}